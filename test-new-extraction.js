#!/usr/bin/env node

/**
 * 测试新的工具调用JSON提取逻辑
 */

// 模拟新的提取函数
const extractToolCallsFromContent = (content) => {
  const toolCalls = [];
  let searchIndex = 0;
  
  while (true) {
    // 查找工具调用JSON的开始位置
    const startIndex = content.indexOf('{"type":"tool_calls"', searchIndex);
    if (startIndex === -1) break;
    
    // 使用括号匹配找到完整的JSON对象
    let braceCount = 0;
    let endIndex = startIndex;
    
    for (let i = startIndex; i < content.length; i++) {
      if (content[i] === '{') {
        braceCount++;
      } else if (content[i] === '}') {
        braceCount--;
        if (braceCount === 0) {
          endIndex = i;
          break;
        }
      }
    }
    
    if (braceCount === 0) {
      const jsonStr = content.substring(startIndex, endIndex + 1);
      
      try {
        const toolCallObj = JSON.parse(jsonStr);
        
        if (toolCallObj && toolCallObj.type === 'tool_calls' && Array.isArray(toolCallObj.tool_calls)) {
          toolCalls.push({
            json: jsonStr,
            data: toolCallObj,
            startIndex,
            endIndex: endIndex + 1
          });
          console.log('✅ 成功提取工具调用JSON');
        }
      } catch (e) {
        console.error('❌ 解析工具调用JSON失败:', e.message);
      }
    }
    
    searchIndex = endIndex + 1;
  }
  
  return toolCalls;
};

// 测试用例
const testCases = [
  {
    name: '基本工具调用',
    content: `我将创作一首诗并保存到文件中。

{"type":"tool_calls","tool_calls":[{"name":"write_file","arguments":{"path":"/Users/<USER>/Desktop/poem.md","content":"# 生命之韵"},"result":{"success":true,"message":"文件已成功创建"},"success":true}]}

好的，我已经将诗保存到了文件中。`
  },
  {
    name: '多个工具调用',
    content: `我将执行多个操作：

{"type":"tool_calls","tool_calls":[{"name":"write_file","arguments":{"path":"poem1.md","content":"春天的诗"},"result":{"success":true},"success":true}]}

第一个文件已创建。现在创建第二个：

{"type":"tool_calls","tool_calls":[{"name":"write_file","arguments":{"path":"poem2.md","content":"夏天的诗"},"result":{"success":true},"success":true}]}

两个文件都已创建完成。`
  },
  {
    name: '嵌套JSON',
    content: `工具调用结果：{"type":"tool_calls","tool_calls":[{"name":"complex_tool","arguments":{"config":{"nested":{"value":"test"}}},"result":{"data":{"items":[1,2,3]}},"success":true}]}`
  }
];

console.log('=== 测试新的工具调用JSON提取逻辑 ===\n');

testCases.forEach((testCase, index) => {
  console.log(`测试 ${index + 1}: ${testCase.name}`);
  console.log('内容长度:', testCase.content.length);
  
  const extracted = extractToolCallsFromContent(testCase.content);
  
  console.log(`提取结果: 找到 ${extracted.length} 个工具调用`);
  
  extracted.forEach((toolCall, toolIndex) => {
    console.log(`  工具调用 ${toolIndex + 1}:`);
    console.log(`    位置: ${toolCall.startIndex} - ${toolCall.endIndex}`);
    console.log(`    工具数量: ${toolCall.data.tool_calls.length}`);
    console.log(`    JSON长度: ${toolCall.json.length}`);
    
    // 验证JSON的完整性
    try {
      const reparsed = JSON.parse(toolCall.json);
      console.log(`    ✅ JSON验证通过`);
    } catch (e) {
      console.log(`    ❌ JSON验证失败: ${e.message}`);
    }
  });
  
  console.log('\n' + '-'.repeat(50) + '\n');
});

console.log('测试完成！');
