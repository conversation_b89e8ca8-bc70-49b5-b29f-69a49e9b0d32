<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具调用JSON解析测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .json-input {
            width: 100%;
            height: 200px;
            font-family: monospace;
            font-size: 12px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>工具调用JSON解析测试</h1>
    
    <div class="test-section">
        <h2>测试1: 基本工具调用JSON</h2>
        <textarea class="json-input" id="test1">{"type":"tool_calls","tool_calls":[{"name":"write_file","arguments":{"path":"/Users/<USER>/Desktop/poem.md","content":"# 生命之韵"},"result":{"success":true,"message":"文件已成功创建"},"success":true}]}</textarea>
        <button onclick="testParsing('test1', 'result1')">测试解析</button>
        <div class="result" id="result1"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2: 多个工具调用JSON</h2>
        <textarea class="json-input" id="test2">{"type":"tool_calls","tool_calls":[{"name":"write_file","arguments":{"path":"poem1.md","content":"春天的诗"},"result":{"success":true},"success":true},{"name":"write_file","arguments":{"path":"poem2.md","content":"夏天的诗"},"result":{"success":true},"success":true}]}</textarea>
        <button onclick="testParsing('test2', 'result2')">测试解析</button>
        <div class="result" id="result2"></div>
    </div>
    
    <div class="test-section">
        <h2>测试3: 失败的工具调用JSON</h2>
        <textarea class="json-input" id="test3">{"type":"tool_calls","tool_calls":[{"name":"write_file","arguments":{"path":"/invalid/path/test.md","content":"测试内容"},"error":"无法创建文件：路径不存在","success":false}]}</textarea>
        <button onclick="testParsing('test3', 'result3')">测试解析</button>
        <div class="result" id="result3"></div>
    </div>
    
    <div class="test-section">
        <h2>测试4: 正则表达式匹配测试</h2>
        <textarea class="json-input" id="test4">我将创作一首诗并保存到文件中。

{"type":"tool_calls","tool_calls":[{"name":"write_file","arguments":{"path":"/Users/<USER>/Desktop/poem.md","content":"# 生命之韵\n\n生命如诗，韵律悠长"},"result":{"success":true,"message":"文件已成功创建"},"success":true}]}

好的，我已经将诗保存到了文件中。</textarea>
        <button onclick="testRegexMatching('test4', 'result4')">测试正则匹配</button>
        <div class="result" id="result4"></div>
    </div>
    
    <script>
        function testParsing(inputId, resultId) {
            const input = document.getElementById(inputId).value;
            const resultDiv = document.getElementById(resultId);
            
            try {
                const parsed = JSON.parse(input);
                
                if (parsed && parsed.type === 'tool_calls' && Array.isArray(parsed.tool_calls)) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ 解析成功</strong><br>
                        类型: ${parsed.type}<br>
                        工具调用数量: ${parsed.tool_calls.length}<br>
                        工具调用详情:<br>
                        <pre>${JSON.stringify(parsed.tool_calls, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '<strong>❌ 格式错误</strong><br>不是有效的工具调用JSON格式';
                }
            } catch (e) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ 解析失败</strong><br>错误: ${e.message}`;
            }
        }
        
        function testRegexMatching(inputId, resultId) {
            const input = document.getElementById(inputId).value;
            const resultDiv = document.getElementById(resultId);
            
            // 使用与前端相同的正则表达式
            const toolCallMatches = input.match(/\{"type"\s*:\s*"tool_calls"[\s\S]*?\}/g);
            
            if (toolCallMatches) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ 正则匹配成功</strong><br>
                    找到 ${toolCallMatches.length} 个匹配项:<br>
                    ${toolCallMatches.map((match, index) => `
                        <div style="margin: 10px 0; padding: 10px; background: white; border-radius: 3px;">
                            <strong>匹配 ${index + 1}:</strong><br>
                            <pre style="font-size: 11px; overflow-x: auto;">${match}</pre>
                        </div>
                    `).join('')}
                `;
                
                // 测试每个匹配项的解析
                toolCallMatches.forEach((match, index) => {
                    try {
                        const parsed = JSON.parse(match);
                        console.log(`匹配 ${index + 1} 解析成功:`, parsed);
                    } catch (e) {
                        console.error(`匹配 ${index + 1} 解析失败:`, e);
                    }
                });
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<strong>❌ 正则匹配失败</strong><br>没有找到工具调用JSON';
            }
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            console.log('页面加载完成，开始自动测试...');
            testParsing('test1', 'result1');
            testParsing('test2', 'result2');
            testParsing('test3', 'result3');
            testRegexMatching('test4', 'result4');
        };
    </script>
</body>
</html>
