import express from 'express';
import cors from 'cors';
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import bodyParser from 'body-parser';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import fetch from 'node-fetch';
import { HttpsProxyAgent } from 'https-proxy-agent';

/**
 * 清理和验证 Anthropic 请求格式
 * @param {Object} requestBody 原始请求体
 * @returns {Object} 清理后的请求体
 */
function cleanAnthropicRequest(requestBody) {
  const { messages, ...otherFields } = requestBody;

  if (!messages || !Array.isArray(messages)) {
    throw new Error('消息列表无效：messages必须是数组');
  }

  if (messages.length === 0) {
    throw new Error('消息列表不能为空');
  }

  // 第一步：基础验证和清理
  const validMessages = [];

  for (let i = 0; i < messages.length; i++) {
    const msg = messages[i];

    // 验证消息结构
    if (!msg || typeof msg !== 'object') {
      logger.warn(`跳过无效消息 [${i}]: 不是对象`);
      continue;
    }

    if (!msg.role || typeof msg.role !== 'string') {
      logger.warn(`跳过无效消息 [${i}]: 缺少或无效的role`);
      continue;
    }

    if (!msg.content || typeof msg.content !== 'string') {
      logger.warn(`跳过无效消息 [${i}]: 缺少或无效的content`);
      continue;
    }

    // 清理内容
    const cleanedContent = msg.content.trim();
    if (cleanedContent.length === 0) {
      logger.warn(`跳过空内容消息 [${i}]: role=${msg.role}`);
      continue;
    }

    // 验证角色
    if (!['user', 'assistant', 'system'].includes(msg.role)) {
      logger.warn(`跳过无效角色消息 [${i}]: role=${msg.role}`);
      continue;
    }

    validMessages.push({
      role: msg.role,
      content: cleanedContent
    });
  }

  if (validMessages.length === 0) {
    throw new Error('没有有效的消息内容，所有消息都被过滤掉了');
  }

  // 第二步：分离system消息和对话消息
  const systemMessages = validMessages.filter(msg => msg.role === 'system');
  const conversationMessages = validMessages.filter(msg => msg.role !== 'system');

  if (conversationMessages.length === 0) {
    throw new Error('没有有效的对话消息（user/assistant），至少需要一条用户消息');
  }

  // 第三步：合并连续的相同角色消息
  const mergedMessages = [];

  for (const msg of conversationMessages) {
    if (mergedMessages.length === 0) {
      mergedMessages.push({ ...msg });
    } else {
      const lastMsg = mergedMessages[mergedMessages.length - 1];
      if (lastMsg.role === msg.role) {
        // 合并相同角色的消息
        lastMsg.content += '\n\n' + msg.content;
        logger.debug(`合并相同角色消息: ${msg.role}`);
      } else {
        mergedMessages.push({ ...msg });
      }
    }
  }

  // 第四步：确保消息序列符合Anthropic要求
  // 1. 移除末尾的assistant消息（Anthropic要求最后一条消息必须是user）
  while (mergedMessages.length > 0 &&
         mergedMessages[mergedMessages.length - 1].role === 'assistant') {
    logger.debug('移除末尾的assistant消息');
    mergedMessages.pop();
  }

  // 2. 确保至少有一条消息
  if (mergedMessages.length === 0) {
    throw new Error('清理后没有有效的消息，请检查消息历史');
  }

  // 3. 确保第一条消息是用户消息
  if (mergedMessages[0].role !== 'user') {
    throw new Error('第一条消息必须是用户消息');
  }

  // 4. 确保最后一条消息是用户消息
  if (mergedMessages[mergedMessages.length - 1].role !== 'user') {
    throw new Error('最后一条消息必须是用户消息');
  }

  // 5. 验证消息交替模式（user -> assistant -> user -> ...）
  for (let i = 0; i < mergedMessages.length; i++) {
    const expectedRole = i % 2 === 0 ? 'user' : 'assistant';
    if (mergedMessages[i].role !== expectedRole) {
      logger.warn(`消息序列不符合交替模式，位置 ${i}: 期望 ${expectedRole}，实际 ${mergedMessages[i].role}`);
      // 不抛出错误，只记录警告，因为Anthropic可以处理一些不规则的序列
    }
  }

  // 第五步：构建最终请求体
  const cleanedBody = {
    ...otherFields,
    messages: mergedMessages
  };

  // 处理system消息
  if (systemMessages.length > 0) {
    // 合并所有system消息
    const combinedSystemMessage = systemMessages
      .map(msg => msg.content)
      .join('\n\n')
      .trim();

    if (combinedSystemMessage.length > 0) {
      cleanedBody.system = combinedSystemMessage;
      logger.debug(`添加system消息，长度: ${combinedSystemMessage.length}`);
    }
  }

  logger.debug(`消息清理完成: 原始 ${messages.length} 条 -> 有效 ${validMessages.length} 条 -> 最终 ${mergedMessages.length} 条`);

  return cleanedBody;
}

// 加载环境变量
dotenv.config();

// 日志工具函数
const logLevels = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3
};

// 默认日志级别，可以从环境变量设置
const LOG_LEVEL = process.env.MCP_LOG_LEVEL || 'INFO';
const CURRENT_LOG_LEVEL = logLevels[LOG_LEVEL] || logLevels.INFO;

// 添加时间戳的日志函数
function logWithTimestamp(level, message, ...args) {
  if (logLevels[level] >= CURRENT_LOG_LEVEL) {
    const timestamp = new Date().toISOString();
    const formattedMessage = `[${timestamp}][${level}] ${message}`;

    if (level === 'ERROR') {
      console.error(formattedMessage, ...args);
    } else if (level === 'WARN') {
      console.warn(formattedMessage, ...args);
    } else {
      console.log(formattedMessage, ...args);
    }
  }
  // 如果是在Electron子进程中运行，发送消息给主进程
  if (process.send) {
    process.send({ level, message, timestamp });
  }
}

// 日志工具对象
const logger = {
  debug: (message, ...args) => logWithTimestamp('DEBUG', message, ...args),
  info: (message, ...args) => logWithTimestamp('INFO', message, ...args),
  warn: (message, ...args) => logWithTimestamp('WARN', message, ...args),
  error: (message, ...args) => logWithTimestamp('ERROR', message, ...args)
};

const app = express();
const port = process.env.MCP_SERVER_PORT || 3001;

// 存储配置的文件路径
const CONFIG_DIR = process.env.MCP_CONFIG_DIR || './config';
const CLIENT_CONFIG_PATH = path.join(CONFIG_DIR, 'clients.json');
const TOOL_MAPPING_PATH = path.join(CONFIG_DIR, 'tool_mappings.json');

// 确保配置目录存在
if (!fs.existsSync(CONFIG_DIR)) {
  fs.mkdirSync(CONFIG_DIR, { recursive: true });
  logger.info(`已创建配置目录: ${CONFIG_DIR}`);
}

// 启用CORS和JSON解析
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

// 存储多个MCP客户端
const mcpClients = {};
const clientConfigs = {};
// 添加工具名到客户端的映射
const toolToClientMap = {};

// 从文件加载客户端配置
function loadClientConfigs() {
  try {
    // 首先清空内存中的所有配置
    logger.debug('清空内存中的所有客户端配置');
    Object.keys(clientConfigs).forEach(key => delete clientConfigs[key]);

    if (fs.existsSync(CLIENT_CONFIG_PATH)) {
      const data = fs.readFileSync(CLIENT_CONFIG_PATH, 'utf8');

      // 检查文件是否为空
      if (!data || data.trim() === '') {
        logger.info(`配置文件 ${CLIENT_CONFIG_PATH} 为空，内存配置已清空`);
        return true; // 返回true表示操作成功
      }

      const loadedConfigs = JSON.parse(data);

      // 加载配置到已清空的内存中
      Object.entries(loadedConfigs).forEach(([name, config]) => {
        clientConfigs[name] = config;
        logger.debug(`已加载客户端配置: ${name}`, config);
      });

      logger.info(`已从 ${CLIENT_CONFIG_PATH} 加载 ${Object.keys(loadedConfigs).length} 个客户端配置`);
      return true;
    } else {
      logger.warn(`配置文件 ${CLIENT_CONFIG_PATH} 不存在，创建空配置文件`);
      fs.writeFileSync(CLIENT_CONFIG_PATH, JSON.stringify({}, null, 2), 'utf8');
      // 内存配置已经清空，无需再次清空
    }
  } catch (error) {
    logger.error(`加载客户端配置失败:`, error);
  }
  return false;
}

// 保存客户端配置到文件
function saveClientConfigs() {
  try {
    fs.writeFileSync(CLIENT_CONFIG_PATH, JSON.stringify(clientConfigs, null, 2), 'utf8');
    logger.info(`已保存 ${Object.keys(clientConfigs).length} 个客户端配置到 ${CLIENT_CONFIG_PATH}`);
    return true;
  } catch (error) {
    logger.error(`保存客户端配置失败:`, error);
    return false;
  }
}

// 保存工具映射到文件
function saveToolMappings() {
  try {
    fs.writeFileSync(TOOL_MAPPING_PATH, JSON.stringify(toolToClientMap, null, 2), 'utf8');
    logger.info(`已保存 ${Object.keys(toolToClientMap).length} 个工具映射到 ${TOOL_MAPPING_PATH}`);
    return true;
  } catch (error) {
    logger.error(`保存工具映射失败:`, error);
    return false;
  }
}

// 从文件加载工具映射
function loadToolMappings() {
  try {
    if (fs.existsSync(TOOL_MAPPING_PATH)) {
      const data = fs.readFileSync(TOOL_MAPPING_PATH, 'utf8');

      // 检查文件是否为空
      if (!data || data.trim() === '') {
        logger.info(`工具映射文件 ${TOOL_MAPPING_PATH} 为空，跳过加载`);
        return false;
      }

      try {
        const loadedMappings = JSON.parse(data);

        // 清空现有映射，加载保存的映射
        Object.keys(toolToClientMap).forEach(key => delete toolToClientMap[key]);

        // 加载映射
        Object.entries(loadedMappings).forEach(([toolName, clientName]) => {
          // 检查客户端是否存在
          if (clientConfigs[clientName]) {
            toolToClientMap[toolName] = clientName;
            logger.debug(`已加载工具映射: ${toolName} -> ${clientName}`);
          } else {
            logger.warn(`工具 ${toolName} 映射到不存在的客户端 ${clientName}，跳过`);
          }
        });

        logger.info(`已从 ${TOOL_MAPPING_PATH} 加载 ${Object.keys(toolToClientMap).length} 个工具映射`);
        return true;
      } catch (parseError) {
        logger.error(`解析工具映射文件失败:`, parseError);
        return false;
      }
    } else {
      logger.info(`工具映射文件 ${TOOL_MAPPING_PATH} 不存在，将创建新的映射文件`);
      // 创建空的映射文件
      fs.writeFileSync(TOOL_MAPPING_PATH, JSON.stringify({}, null, 2), 'utf8');
    }
  } catch (error) {
    logger.error(`加载工具映射失败:`, error);
  }
  return false;
}

// 清理所有已连接的客户端
async function cleanupClients() {
  const clientCount = Object.keys(mcpClients).length;
  if (clientCount > 0) {
    logger.info(`开始清理 ${clientCount} 个已连接的客户端...`);
  }

  for (const clientName of Object.keys(mcpClients)) {
    try {
      await closeClient(clientName);
      logger.info(`已清理客户端: ${clientName}`);
    } catch (error) {
      logger.error(`清理客户端 ${clientName} 失败:`, error);
    }
  }
}

// 初始化工具到客户端的映射
async function initializeToolMappings(forceReconnect = false) {
  try {
    logger.info(`开始初始化工具到客户端的映射...`);

    // 清空现有的工具映射
    Object.keys(toolToClientMap).forEach(key => delete toolToClientMap[key]);
    logger.debug('已清空工具映射');

    // 如果不强制重连，先尝试从文件加载映射
    if (!forceReconnect) {
      const loadedFromFile = loadToolMappings();

      if (loadedFromFile && Object.keys(toolToClientMap).length > 0) {
        logger.info(`已从文件加载工具映射，跳过连接初始化`);
        return;
      }
    } else {
      logger.info('强制重新连接并获取工具映射');
    }

    // 获取所有已配置客户端的名称
    const clientNames = Object.keys(clientConfigs);

    if (clientNames.length === 0) {
      logger.info(`没有找到已配置的客户端，工具映射初始化跳过`);
      return;
    }

    let successCount = 0;
    let failCount = 0;
    let toolCount = 0;

    // 遍历每个客户端，尝试连接并获取工具
    for (const clientName of clientNames) {
      try {
        logger.debug(`正在初始化客户端 ${clientName} 的工具映射...`);

        // 初始化客户端
        const client = await initMCPClient(clientName);

        // 获取工具列表
        const tools = await client.listTools();
        logger.debug(`客户端 ${clientName} 可用工具列表:`, tools);

        // 检查工具列表结构
        if (!tools || !tools.tools || !Array.isArray(tools.tools)) {
          logger.warn(`客户端 ${clientName} 返回的工具列表格式不正确:`, tools);
          // 继续处理，不终止流程
        } else {
          // 更新工具映射
          tools.tools.forEach(tool => {
            if (tool && tool.name) {
              toolToClientMap[tool.name] = clientName;
              toolCount++;
              logger.debug(`已将工具 ${tool.name} 映射到客户端 ${clientName}`);
            }
          });

          logger.info(`已为客户端 ${clientName} 映射 ${tools.tools.length} 个工具`);
        }
        successCount++;
      } catch (error) {
        logger.error(`初始化客户端 ${clientName} 的工具映射失败:`, error);
        failCount++;
      }
    }

    logger.info(`工具映射初始化完成: ${successCount} 个客户端成功, ${failCount} 个失败, 共映射 ${toolCount} 个工具`);

    // 保存工具映射到文件
    saveToolMappings();

    // 打印工具映射结果
    logger.debug(`工具到客户端映射结果:`, toolToClientMap);
  } catch (error) {
    logger.error(`初始化工具映射失败:`, error);
  }
}

// 加载客户端配置前先清理
cleanupClients().then(() => {
  logger.info('开始加载客户端配置...');
  if (loadClientConfigs()) {
    // 配置加载成功后初始化工具映射
    initializeToolMappings().catch(error => {
      logger.error('初始化工具映射时出错:', error);
    });
  }
}).catch(error => {
  logger.error('清理客户端时出错:', error);
  loadClientConfigs(); // 即使清理失败也继续加载配置
  // 配置加载后初始化工具映射
  initializeToolMappings().catch(error => {
    logger.error('初始化工具映射时出错:', error);
  });
});

// 初始化指定的MCP客户端
async function initMCPClient(clientName) {
  if (mcpClients[clientName]) {
    logger.debug(`客户端 ${clientName} 已初始化，重用现有实例`);
    return mcpClients[clientName];
  }

  try {
    const config = clientConfigs[clientName];
    if (!config) {
      throw new Error(`未找到客户端配置: ${clientName}`);
    }

    logger.info(`初始化客户端 ${clientName}，命令: ${config.command} ${config.args.join(' ')}`);

    const transport = new StdioClientTransport({
      command: config.command,
      args: config.args
    });

    const client = new Client(
      {
        name: `mcp-chat-${clientName}-client`,
        version: "1.0.0"
      },
      {
        capabilities: {
          prompts: {},
          resources: {},
          tools: {}
        }
      }
    );

    try {
      logger.debug(`连接到客户端 ${clientName} 中...`);
      await client.connect(transport);

      logger.info(`已连接到MCP服务: ${clientName}`);
      // 列出可用工具
      try {
        const tools = await client.listTools();
        logger.debug(`客户端 ${clientName} 可用工具列表:`, tools);

        // 检查工具列表结构
        if (!tools || !tools.tools || !Array.isArray(tools.tools)) {
          logger.warn(`客户端 ${clientName} 返回的工具列表格式不正确:`, tools);
          // 继续处理，不终止流程
        } else {
          // 更新工具到客户端的映射
          tools.tools.forEach(tool => {
            if (tool && tool.name) {
              toolToClientMap[tool.name] = clientName;
              logger.debug(`已将工具 ${tool.name} 映射到客户端 ${clientName}`);
            }
          });

          // 保存更新后的映射
          saveToolMappings();
        }
      } catch (toolError) {
        logger.error(`获取客户端 ${clientName} 的工具列表失败:`, toolError);
        // 继续处理，不终止流程
      }

      mcpClients[clientName] = client;
      return client;
    } catch (connectError) {
      logger.error(`连接到客户端 ${clientName} 失败:`, connectError);
      throw new Error(`连接到客户端失败: ${connectError.message}`);
    }
  } catch (error) {
    logger.error(`MCP客户端 ${clientName} 初始化错误:`, error);
    throw error;
  }
}

// 关闭并清理指定的客户端
async function closeClient(clientName) {
  const client = mcpClients[clientName];
  if (client) {
    logger.debug(`开始关闭客户端 ${clientName}...`);
    try {
      client.onclose = () => {
        logger.info(`MCP客户端 ${clientName} 已断开连接`);
      }
      // 清理工具映射
      for (const [toolName, mappedClientName] of Object.entries(toolToClientMap)) {
        if (mappedClientName === clientName) {
          delete toolToClientMap[toolName];
          logger.debug(`已移除工具 ${toolName} 与客户端 ${clientName} 的映射`);
        }
      }

      delete mcpClients[clientName];
      return true;
    } catch (error) {
      logger.error(`断开MCP客户端 ${clientName} 连接失败:`, error);
      // 即使失败也删除引用，防止客户端残留
      delete mcpClients[clientName];
      throw error;
    }
  } else {
    logger.debug(`客户端 ${clientName} 未连接，无需断开`);
  }
  return false;
}

// API路由

// Anthropic API代理路由

// Anthropic模型列表代理路由
app.get('/api/anthropic/models', async (req, res) => {
  try {
    logger.debug('API请求: Anthropic模型列表');
    logger.debug('请求头:', JSON.stringify(req.headers));

    // 获取请求头中的API密钥
    const apiKey = req.headers['x-api-key'] || req.headers['authorization']?.replace('Bearer ', '');
    if (!apiKey) {
      logger.error('Anthropic模型列表请求缺少API密钥');
      return res.status(400).json({ error: '缺少API密钥' });
    } else {
      // 安全打印API密钥前几位和后几位
      const maskedKey = apiKey.substring(0, 5) + '...' + apiKey.substring(apiKey.length - 5);
      logger.debug(`收到API密钥: ${maskedKey}`);
    }

    try {
      // 使用代理调用Anthropic API
      logger.debug('尝试通过代理调用Anthropic API...');

      // 配置代理
      const proxyUrl = 'http://127.0.0.1:7897';
      const agent = new HttpsProxyAgent(proxyUrl);
      logger.debug(`使用代理: ${proxyUrl}`);

      const response = await fetch('https://api.anthropic.com/v1/models', {
        method: 'GET',
        headers: {
          'x-api-key': apiKey,
          'anthropic-version': '2023-06-01',
          'Content-Type': 'application/json'
        },
        agent: agent
      });

      logger.debug(`API响应状态: ${response.status}`);

      if (!response.ok) {
        let errorText = '';
        let errorJson = {};

        try {
          // 尝试解析错误响应为JSON
          errorText = await response.text();
          try {
            errorJson = JSON.parse(errorText);
            logger.error(`Anthropic API错误响应(${response.status}):`, JSON.stringify(errorJson, null, 2));
          } catch (jsonError) {
            logger.error(`Anthropic API错误响应(${response.status}):`, errorText);
          }

          // 针对特定状态码提供更详细的错误信息
          if (response.status === 403) {
            logger.error('API密钥认证失败: 403 Forbidden - 可能是API密钥无效或没有足够权限');
            logger.info('返回模拟模型列表用于测试');

            // 返回模拟的 Anthropic 模型列表
            const mockModels = {
              "data": [
                {
                  "id": "claude-3-5-sonnet-20241022",
                  "display_name": "Claude 3.5 Sonnet",
                  "created_at": "2024-10-22T00:00:00Z",
                  "type": "model"
                },
                {
                  "id": "claude-3-5-haiku-20241022",
                  "display_name": "Claude 3.5 Haiku",
                  "created_at": "2024-10-22T00:00:00Z",
                  "type": "model"
                },
                {
                  "id": "claude-3-opus-20240229",
                  "display_name": "Claude 3 Opus",
                  "created_at": "2024-02-29T00:00:00Z",
                  "type": "model"
                },
                {
                  "id": "claude-3-sonnet-20240229",
                  "display_name": "Claude 3 Sonnet",
                  "created_at": "2024-02-29T00:00:00Z",
                  "type": "model"
                },
                {
                  "id": "claude-3-haiku-20240307",
                  "display_name": "Claude 3 Haiku",
                  "created_at": "2024-03-07T00:00:00Z",
                  "type": "model"
                }
              ],
              "first_id": "claude-3-5-sonnet-20241022",
              "has_more": false,
              "last_id": "claude-3-haiku-20240307"
            };

            return res.json(mockModels);
          } else if (response.status === 401) {
            logger.error('API密钥认证失败: 401 Unauthorized - 未提供有效的API密钥');
            return res.status(response.status).json({
              error: '认证失败: 未提供有效的API密钥',
              details: errorJson,
              originalError: errorText
            });
          }

          return res.status(response.status).json({
            error: `Anthropic API错误: ${response.status}`,
            details: errorJson,
            originalError: errorText
          });
        } catch (parseError) {
          logger.error(`解析Anthropic API错误响应失败:`, parseError);
          return res.status(response.status).json({
            error: `Anthropic API错误: ${response.status}`,
            originalError: errorText
          });
        }
      }

      const data = await response.json();
      logger.debug(`获取到Anthropic模型列表原始数据: ${JSON.stringify(data)}`);
      logger.debug(`获取到Anthropic模型列表: ${data.data?.length || 0}个模型`);

      res.json(data);
    } catch (fetchError) {
      logger.error('Anthropic API请求错误:', fetchError);
      logger.error('错误详情:', fetchError.stack);
      logger.error('错误消息:', fetchError.message);
      res.status(500).json({ error: `Anthropic API请求错误: ${fetchError.message}` });
    }
  } catch (error) {
    logger.error('Anthropic模型列表代理错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// Anthropic 消息发送代理路由
app.post('/api/anthropic/messages', async (req, res) => {
  try {
    logger.debug('API请求: Anthropic 消息发送');
    logger.debug('请求体:', JSON.stringify(req.body, null, 2));

    // 获取请求头中的API密钥
    const apiKey = req.headers['x-api-key'] || req.headers['authorization']?.replace('Bearer ', '');
    if (!apiKey) {
      logger.error('Anthropic 消息发送请求缺少API密钥');
      return res.status(400).json({ error: '缺少API密钥' });
    }

    const isStream = req.body.stream === true;
    logger.debug(`Anthropic 消息发送请求, 流式: ${isStream}`);

    // 清理和验证消息格式
    const cleanedBody = cleanAnthropicRequest(req.body);
    logger.debug('清理后的请求体:', JSON.stringify(cleanedBody, null, 2));

    // 配置代理
    const proxyUrl = 'http://127.0.0.1:7897';
    const agent = new HttpsProxyAgent(proxyUrl);
    logger.debug(`使用代理: ${proxyUrl}`);

    // 构建请求头
    const headers = {
      'Content-Type': 'application/json',
      'x-api-key': apiKey,
      'anthropic-version': '2023-06-01'
    };

    // 发送请求到Anthropic API
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers,
      body: JSON.stringify(cleanedBody),
      agent: agent
    });

    logger.debug(`Anthropic API响应状态: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      logger.error(`Anthropic API错误: ${response.status}`, errorText);
      return res.status(response.status).json({
        error: `Anthropic API错误: ${response.status}`,
        details: errorText
      });
    }

    // 如果是流式响应
    if (isStream) {
      // 设置响应头
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

      // 获取响应流
      const stream = response.body;

      if (stream) {
        // 使用 Node.js 流处理
        stream.on('data', (chunk) => {
          res.write(chunk);
        });

        stream.on('end', () => {
          logger.debug('Anthropic 流式响应结束');
          res.end();
        });

        stream.on('error', (err) => {
          logger.error('Anthropic API流错误:', err);
          res.end();
        });
      } else {
        logger.error('无法获取响应流');
        res.status(500).json({ error: '无法获取响应流' });
      }
    } else {
      // 非流式响应，直接返回JSON
      const data = await response.json();
      logger.debug('Anthropic API响应:', JSON.stringify(data, null, 2));
      res.json(data);
    }
  } catch (error) {
    logger.error('Anthropic 消息发送代理错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 获取当前工具映射
app.get('/mcp/tools/mappings', (req, res) => {
  try {
    logger.debug(`API请求: 获取当前工具映射`);

    const mappings = {};
    Object.entries(toolToClientMap).forEach(([toolName, clientName]) => {
      // 检查客户端是否存在
      if (clientConfigs[clientName]) {
        mappings[toolName] = clientName;
      }
    });

    res.json({
      success: true,
      mappings: mappings,
      count: Object.keys(mappings).length
    });
  } catch (error) {
    logger.error(`获取工具映射失败:`, error);
    res.status(500).json({ error: "获取工具映射失败", message: error.message });
  }
});

// 强制重新初始化工具映射
app.post('/mcp/tools/reinitialize', async (req, res) => {
  try {
    logger.debug(`API请求: 强制重新初始化工具映射`);

    // 调用初始化函数，传入true表示强制重连
    await initializeToolMappings(true);

    logger.info(`工具映射重新初始化完成`);
    res.json({
      success: true,
      message: `工具映射重新初始化完成`,
      mappingCount: Object.keys(toolToClientMap).length
    });
  } catch (error) {
    logger.error(`强制重新初始化工具映射失败:`, error);
    res.status(500).json({ error: "强制重新初始化工具映射失败", message: error.message });
  }
});

// 清空所有客户端配置
app.post('/mcp/clients/clear', async (req, res) => {
  try {
    logger.debug(`API请求: 清空所有客户端配置`);

    // 先断开所有连接的客户端
    await cleanupClients();

    // 清空配置对象
    Object.keys(clientConfigs).forEach(key => delete clientConfigs[key]);

    // 清空工具映射
    Object.keys(toolToClientMap).forEach(key => delete toolToClientMap[key]);

    // 保存空配置到文件
    saveClientConfigs();
    saveToolMappings();

    logger.info(`已清空所有客户端配置和工具映射`);
    res.json({ success: true, message: `已清空所有客户端配置和工具映射` });
  } catch (error) {
    logger.error(`清空客户端配置失败:`, error);
    res.status(500).json({ error: "清空客户端配置失败", message: error.message });
  }
});

// 获取所有已配置的客户端列表
app.get('/mcp/clients', (req, res) => {
  logger.debug(`API请求: 获取所有客户端列表`);
  const clients = Object.keys(clientConfigs).map(name => ({
    name,
    ...clientConfigs[name],
    isConnected: !!mcpClients[name]
  }));
  logger.debug(`返回 ${clients.length} 个客户端信息`);
  res.json({ clients });
});

// 根据服务器ID获取相关的客户端列表
app.get('/mcp/clients/by-server/:serverId', (req, res) => {
  try {
    const { serverId } = req.params;
    logger.debug(`API请求: 获取服务器 ${serverId} 的相关客户端列表`);

    // 先查找服务器配置
    if (!clientConfigs[serverId]) {
      logger.warn(`未找到服务器: ${serverId}`);
      return res.status(404).json({ error: `未找到服务器: ${serverId}` });
    }

    const serverConfig = clientConfigs[serverId];

    // 查找相同命令和参数的客户端配置
    const relatedClients = Object.entries(clientConfigs)
      .filter(([name, config]) =>
        config.command === serverConfig.command &&
        JSON.stringify(config.args) === JSON.stringify(serverConfig.args)
      )
      .map(([name, config]) => ({
        name,
        ...config,
        isConnected: !!mcpClients[name]
      }));

    logger.debug(`找到 ${relatedClients.length} 个相关客户端`);
    res.json({
      serverId,
      clients: relatedClients
    });
  } catch (error) {
    logger.error(`获取服务器 ${req.params.serverId} 的客户端列表失败:`, error);
    res.status(500).json({ error: "获取客户端列表失败", message: error.message });
  }
});

// 获取特定客户端的详细信息
app.get('/mcp/clients/:clientName', (req, res) => {
  const { clientName } = req.params;
  logger.debug(`API请求: 获取客户端 ${clientName} 的详细信息`);

  if (!clientConfigs[clientName]) {
    logger.warn(`未找到客户端: ${clientName}`);
    return res.status(404).json({ error: `未找到客户端: ${clientName}` });
  }

  const clientInfo = {
    name: clientName,
    ...clientConfigs[clientName],
    isConnected: !!mcpClients[clientName]
  };

  res.json(clientInfo);
});

// 添加/更新客户端配置
app.post('/mcp/clients', async (req, res) => {
  try {
    const { name, command, args, description, autoConnect } = req.body;

    logger.debug(`API请求: 添加/更新客户端 ${name}`);

    if (!name || !command || !args) {
      logger.warn(`添加/更新客户端缺少必要参数`);
      return res.status(400).json({ error: "缺少必要参数，需要提供 name, command 和 args" });
    }

    logger.info(`添加/更新客户端 ${name}，命令: ${command}，自动连接=${autoConnect}`);

    // 如果客户端已连接，需要先断开连接
    if (mcpClients[name]) {
      logger.debug(`客户端 ${name} 已连接，需要先断开连接`);
      try {
        await closeClient(name);
      } catch (error) {
        logger.error(`断开已有客户端 ${name} 连接失败:`, error);
        // 继续处理，不终止流程
      }
    }

    // 添加/更新配置
    clientConfigs[name] = {
      command,
      args,
      description: description || `MCP客户端: ${name}`,
      lastUpdated: new Date().toISOString()
    };

    // 保存配置到文件
    saveClientConfigs();

    // 如果设置了自动连接
    if (autoConnect) {
      logger.debug(`尝试自动连接客户端 ${name}`);
      try {
        await initMCPClient(name);
        logger.info(`客户端 ${name} 已配置并成功连接`);
        res.json({
          success: true,
          message: `客户端 ${name} 配置已保存并成功连接`,
          client: {
            name,
            ...clientConfigs[name],
            isConnected: true
          }
        });
      } catch (connectError) {
        logger.warn(`客户端 ${name} 配置已保存，但连接失败: ${connectError.message}`);
        res.status(207).json({
          success: true,
          configSaved: true,
          connectionError: connectError.message,
          message: `客户端 ${name} 配置已保存，但连接失败: ${connectError.message}`,
          client: {
            name,
            ...clientConfigs[name],
            isConnected: false
          }
        });
      }
    } else {
      logger.info(`客户端 ${name} 配置已保存（未自动连接）`);
      res.json({
        success: true,
        message: `客户端 ${name} 配置已保存`,
        client: {
          name,
          ...clientConfigs[name],
          isConnected: false
        }
      });
    }
  } catch (error) {
    logger.error(`添加/更新客户端配置失败:`, error);
    res.status(500).json({ error: "添加/更新客户端配置失败", message: error.message });
  }
});

// 删除客户端配置
app.delete('/mcp/clients/:clientName', async (req, res) => {
  try {
    const { clientName } = req.params;
    logger.debug(`API请求: 删除客户端 ${clientName}`);

    if (!clientConfigs[clientName]) {
      logger.warn(`删除失败: 未找到客户端 ${clientName}`);
      return res.status(404).json({ error: `未找到客户端: ${clientName}` });
    }

    // 如果客户端已连接，先断开连接
    if (mcpClients[clientName]) {
      logger.debug(`客户端 ${clientName} 已连接，需要先断开连接`);
      await closeClient(clientName);
    }

    // 删除配置
    delete clientConfigs[clientName];
    logger.info(`客户端 ${clientName} 配置已删除`);

    // 保存配置到文件
    saveClientConfigs();

    res.json({ success: true, message: `客户端 ${clientName} 已删除` });
  } catch (error) {
    logger.error(`删除客户端配置失败:`, error);
    res.status(500).json({ error: "删除客户端配置失败", message: error.message });
  }
});

// 导出所有客户端配置
app.get('/mcp/clients/export', (req, res) => {
  logger.debug(`API请求: 导出所有客户端配置`);
  logger.info(`正在导出 ${Object.keys(clientConfigs).length} 个客户端配置`);

  res.setHeader('Content-Type', 'application/json');
  res.setHeader('Content-Disposition', 'attachment; filename=mcp_clients_config.json');
  res.json(clientConfigs);
});

// 连接特定客户端
app.post('/mcp/clients/:clientName/connect', async (req, res) => {
  try {
    const { clientName } = req.params;
    logger.debug(`API请求: 连接客户端 ${clientName}`);

    if (!clientConfigs[clientName]) {
      logger.warn(`连接失败: 未找到客户端配置 ${clientName}`);
      return res.status(404).json({ error: `未找到客户端: ${clientName}` });
    }

    logger.info(`尝试连接客户端: ${clientName}`);

    // 如果客户端已连接，先断开再重连
    if (mcpClients[clientName]) {
      logger.debug(`客户端 ${clientName} 已连接，将断开后重新连接`);
      try {
        await closeClient(clientName);
      } catch (error) {
        logger.error(`断开已有客户端 ${clientName} 连接失败:`, error);
        // 继续处理，不终止流程
      }
    }
    // 连接客户端
    const client = await initMCPClient(clientName);

    // 确保客户端成功初始化
    if (!client) {
      logger.error(`客户端 ${clientName} 初始化失败`);
      throw new Error(`初始化客户端 ${clientName} 失败`);
    }
    logger.info(`客户端 ${clientName} 连接成功`);
    res.json({
      success: true,
      message: `客户端 ${clientName} 已连接`,
      client: {
        name: clientName,
        ...clientConfigs[clientName],
        isConnected: true
      }
    });
  } catch (error) {
    logger.error(`连接客户端 ${req.params.clientName} 失败:`, error);
    res.status(500).json({ error: `连接客户端失败`, message: error.message });
  }
});

// 断开特定客户端
app.post('/mcp/clients/:clientName/disconnect', async (req, res) => {
  try {
    const { clientName } = req.params;
    logger.debug(`API请求: 断开客户端 ${clientName} 连接`);

    if (!clientConfigs[clientName]) {
      logger.warn(`断开连接失败: 未找到客户端配置 ${clientName}`);
      return res.status(404).json({ error: `未找到客户端: ${clientName}` });
    }

    // 断开客户端
    const wasConnected = await closeClient(clientName);

    if (wasConnected) {
      logger.info(`客户端 ${clientName} 已断开连接`);
      res.json({
        success: true,
        message: `客户端 ${clientName} 已断开连接`,
        client: {
          name: clientName,
          ...clientConfigs[clientName],
          isConnected: false
        }
      });
    } else {
      logger.info(`客户端 ${clientName} 本来就未连接`);
      res.json({
        success: true,
        message: `客户端 ${clientName} 本来就未连接`,
        client: {
          name: clientName,
          ...clientConfigs[clientName],
          isConnected: false
        }
      });
    }
  } catch (error) {
    logger.error(`断开客户端 ${req.params.clientName} 连接失败:`, error);
    res.status(500).json({ error: `断开客户端连接失败`, message: error.message });
  }
});

// 获取指定客户端的可用工具列表
app.get('/mcp/clients/:clientName/tools', async (req, res) => {
  try {
    const { clientName } = req.params;
    logger.debug(`API请求: 获取客户端 ${clientName} 的工具列表`);

    if (!clientConfigs[clientName]) {
      logger.warn(`获取工具列表失败: 未找到客户端配置 ${clientName}`);
      return res.status(404).json({ error: `未找到客户端: ${clientName}` });
    }

    let client = mcpClients[clientName];
    if (!client) {
      logger.info(`客户端 ${clientName} 未连接，尝试连接...`);
      client = await initMCPClient(clientName);
    }

    logger.debug(`正在获取客户端 ${clientName} 的工具列表...`);
    const tools = await client.listTools();
    logger.info(`成功获取客户端 ${clientName} 的工具列表，共 ${tools.length} 个工具`);

    // 更新工具到客户端的映射
    tools.tools.forEach(tool => {
      toolToClientMap[tool.name] = clientName;
      logger.debug(`已将工具 ${tool.name} 映射到客户端 ${clientName}`);
    });

    // 保存更新后的工具映射
    saveToolMappings();

    res.json({
      client: clientName,
      tools
    });
  } catch (error) {
    logger.error(`获取客户端 ${req.params.clientName} 工具列表失败:`, error);
    res.status(500).json({ error: "获取工具列表失败", message: error.message });
  }
});

// 调用指定客户端的工具
app.post('/mcp/clients/:clientName/tools/call', async (req, res) => {
  try {
    const { clientName } = req.params;
    const { name, arguments: args } = req.body;

    if (!name) {
      logger.warn(`调用工具失败: 缺少工具名称`);
      return res.status(400).json({ error: "缺少工具名称" });
    }

    if (!clientConfigs[clientName]) {
      return res.status(404).json({ error: `未找到客户端: ${clientName}` });
    }

    let client = mcpClients[clientName];
    if (!client) {
      client = await initMCPClient(clientName);
    }

    const result = await client.callTool({
      name,
      arguments: args || {}
    });

    // 更新工具到客户端的映射
    toolToClientMap[name] = clientName;
    // 保存更新后的工具映射
    saveToolMappings();

    logger.info(`成功调用客户端 ${clientName} 的工具 ${name}`);
    logger.debug(`工具 ${name} 调用结果:`, result);

    res.json({
      client: clientName,
      tool: name,
      result
    });
  } catch (error) {
    logger.error(`调用客户端 ${req.params.clientName} 的工具失败:`, error);
    logger.debug(`调用工具失败的请求体:`, req.body);
    res.status(500).json({ error: "工具调用失败", message: error.message });
  }
});

// 调用工具（兼容接口，支持自动查找客户端）
app.post('/mcp/tools/call', async (req, res) => {
  try {
    const { name, arguments: args, clientName } = req.body;

    if (!name) {
      logger.warn(`调用工具失败: 缺少工具名称`);
      return res.status(400).json({ error: "缺少工具名称" });
    }

    // 获取客户端名称，优先使用提供的clientName，如果没有则查找映射
    let targetClientName = clientName;

    if (!targetClientName) {
      targetClientName = toolToClientMap[name];

      if (!targetClientName) {
        logger.warn(`调用工具失败: 未找到工具 ${name} 对应的客户端`);
        return res.status(404).json({ error: `未找到工具 ${name} 对应的客户端，请确保工具名称正确或指定clientName` });
      }

      logger.info(`未提供clientName，根据工具 ${name} 自动选择客户端 ${targetClientName}`);
    }

    logger.debug(`API请求(兼容): 调用工具: ${name}，使用客户端: ${targetClientName}`);

    if (!mcpClients[targetClientName]) {
      logger.info(`客户端 ${targetClientName} 未连接，尝试连接...`);
      await initMCPClient(targetClientName);
    }

    const client = mcpClients[targetClientName];
    if (!client) {
      return res.status(500).json({ error: `客户端 ${targetClientName} 连接失败` });
    }

    const result = await client.callTool({
      name,
      arguments: args || {}
    });

    // 更新工具到客户端的映射（确保调用成功的工具被正确映射）
    toolToClientMap[name] = targetClientName;
    // 保存更新后的工具映射
    saveToolMappings();

    logger.info(`成功调用客户端 ${targetClientName} 的工具 ${name}`);
    logger.debug(`工具 ${name} 调用结果:`, result);

    res.json({ result });
  } catch (error) {
    logger.error(`调用工具失败:`, error);
    logger.debug(`调用工具失败的请求体:`, req.body);
    res.status(500).json({ error: "工具调用失败", message: error.message });
  }
});

// 获取指定客户端的可用资源列表
app.get('/mcp/clients/:clientName/resources', async (req, res) => {
  try {
    const { clientName } = req.params;
    logger.debug(`API请求: 获取客户端 ${clientName} 的资源列表`);

    if (!clientConfigs[clientName]) {
      logger.warn(`获取资源列表失败: 未找到客户端配置 ${clientName}`);
      return res.status(404).json({ error: `未找到客户端: ${clientName}` });
    }

    let client = mcpClients[clientName];
    if (!client) {
      logger.info(`客户端 ${clientName} 未连接，尝试连接...`);
      client = await initMCPClient(clientName);
    }

    logger.debug(`正在获取客户端 ${clientName} 的资源列表...`);
    const resources = await client.listResources();
    logger.info(`成功获取客户端 ${clientName} 的资源列表，共 ${resources.length} 个资源`);

    res.json({
      client: clientName,
      resources
    });
  } catch (error) {
    logger.error(`获取客户端 ${req.params.clientName} 资源列表失败:`, error);
    res.status(500).json({ error: "获取资源列表失败", message: error.message });
  }
});

// 获取指定客户端的可用提示列表
app.get('/mcp/clients/:clientName/prompts', async (req, res) => {
  try {
    const { clientName } = req.params;
    logger.debug(`API请求: 获取客户端 ${clientName} 的提示列表`);

    if (!clientConfigs[clientName]) {
      logger.warn(`获取提示列表失败: 未找到客户端配置 ${clientName}`);
      return res.status(404).json({ error: `未找到客户端: ${clientName}` });
    }

    let client = mcpClients[clientName];
    if (!client) {
      logger.info(`客户端 ${clientName} 未连接，尝试连接...`);
      client = await initMCPClient(clientName);
    }

    logger.debug(`正在获取客户端 ${clientName} 的提示列表...`);
    const prompts = await client.listPrompts();
    logger.info(`成功获取客户端 ${clientName} 的提示列表，共 ${prompts.length} 个提示`);

    res.json({
      client: clientName,
      prompts
    });
  } catch (error) {
    logger.error(`获取客户端 ${req.params.clientName} 提示列表失败:`, error);
    res.status(500).json({ error: "获取提示列表失败", message: error.message });
  }
});

// ===== 兼容旧接口 =====
// 这些API需要客户端指定要使用哪个MCP客户端

// 获取可用工具列表
app.get('/mcp/tools', async (req, res) => {
  try {
    const { client: clientName } = req.query;
    logger.debug(`API请求(兼容): 获取客户端 ${clientName} 的工具列表`);
    if (!clientConfigs[clientName]) {
      logger.warn(`获取工具列表失败: 未找到客户端配置 ${clientName}`);
      return res.status(404).json({ error: `未找到客户端: ${clientName}` });
  }
    let client = mcpClients[clientName];
    if (!client) {
      logger.info(`客户端 ${clientName} 未连接，尝试连接...`);
      client = await initMCPClient(clientName);
    }
    const tools = await client.listTools();
    logger.info(`成功获取客户端 ${clientName} 的工具列表，共 ${tools.length} 个工具`);

    res.json({ client: clientName, tools });
  } catch (error) {
    logger.error(`获取工具列表失败:`, error);
    res.status(500).json({ error: "获取工具列表失败", message: error.message });
  }
});

// 调用工具
app.post('/mcp/tools/call', async (req, res) => {
  try {
    const { name, arguments: args, clientName } = req.body;

    if (!name) {
      return res.status(400).json({ error: "缺少工具名称" });
    }

    logger.debug(`API请求(兼容): 调用工具: ${name}`);

    const client = mcpClients[clientName];

    const result = await client.callTool({
      name,
      arguments: args || {}
    });

    logger.info(`成功调用工具 ${name}`);
    logger.debug(`工具 ${name} 调用结果:`, result);

    res.json({ result });
  } catch (error) {
    logger.error(`调用工具失败:`, error);
    logger.debug(`调用工具失败的请求体:`, req.body);
    res.status(500).json({ error: "工具调用失败", message: error.message });
  }
});

// 获取可用资源列表
app.get('/mcp/resources', async (req, res) => {
  try {
    const { client: clientName } = req.query;
    logger.debug(`API请求(兼容): 获取客户端 ${clientName} 的资源列表`);

    if (!clientName) {
      logger.warn(`获取资源列表失败: 缺少client参数`);
      return res.status(400).json({ error: "必须指定client参数" });
    }

    if (!clientConfigs[clientName]) {
      logger.warn(`获取资源列表失败: 未找到客户端配置 ${clientName}`);
      return res.status(404).json({ error: `未找到客户端: ${clientName}` });
    }

    let client = mcpClients[clientName];
    if (!client) {
      logger.info(`客户端 ${clientName} 未连接，尝试连接...`);
      client = await initMCPClient(clientName);
    }

    logger.debug(`正在获取客户端 ${clientName} 的资源列表...`);
    const resources = await client.listResources();
    logger.info(`成功获取客户端 ${clientName} 的资源列表，共 ${resources.length} 个资源`);

    res.json({ client: clientName, resources });
  } catch (error) {
    logger.error(`获取资源列表失败:`, error);
    res.status(500).json({ error: "获取资源列表失败", message: error.message });
  }
});

// 获取可用提示列表
app.get('/mcp/prompts', async (req, res) => {
  try {
    const { client: clientName } = req.query;
    logger.debug(`API请求(兼容): 获取客户端 ${clientName} 的提示列表`);

    if (!clientName) {
      logger.warn(`获取提示列表失败: 缺少client参数`);
      return res.status(400).json({ error: "必须指定client参数" });
    }

    logger.debug(`正在获取客户端 ${clientName} 的提示列表...`);
    const prompts = await client.listPrompts();
    logger.info(`成功获取客户端 ${clientName} 的提示列表，共 ${prompts.length} 个提示`);

    res.json({ client: clientName, prompts });
  } catch (error) {
    logger.error(`获取提示列表失败:`, error);
    res.status(500).json({ error: "获取提示列表失败", message: error.message });
  }
});


// 启动服务器
app.listen(port, () => {
  logger.info(`MCP后端服务运行在 http://localhost:${port}`);
  logger.info(`客户端配置保存位置: ${CLIENT_CONFIG_PATH}`);
  logger.info(`工具映射保存位置: ${TOOL_MAPPING_PATH}`);
  logger.info(`日志级别: ${LOG_LEVEL}`);

  // 显示工具映射信息
  const toolCount = Object.keys(toolToClientMap).length;
  if (toolCount > 0) {
    logger.info(`已加载 ${toolCount} 个工具映射`);
    if (CURRENT_LOG_LEVEL <= logLevels.DEBUG) {
      logger.debug(`工具映射详情:`, toolToClientMap);
    }
  } else {
    logger.info(`尚未加载任何工具映射，等待初始化完成`);
  }
});