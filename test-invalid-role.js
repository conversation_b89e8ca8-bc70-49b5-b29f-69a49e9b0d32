#!/usr/bin/env node

/**
 * 测试无效角色过滤
 */

import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

async function testInvalidRole() {
  console.log('=== 测试无效角色过滤 ===\n');
  
  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 500,
    messages: [
      {
        role: 'user',
        content: 'Hello'
      },
      {
        role: 'invalid_role',
        content: 'This should be filtered out'
      },
      {
        role: 'user',
        content: 'Are you there?'
      }
    ],
    temperature: 0.7
  };
  
  console.log('发送包含无效角色的请求:', JSON.stringify(requestBody, null, 2));
  
  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log('响应状态:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 无效角色过滤成功');
      console.log('响应数据:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('❌ 无效角色过滤失败:', errorText);
      
      // 解析错误信息
      try {
        const errorData = JSON.parse(errorText);
        if (errorData.error && errorData.error.includes('invalid_request_error')) {
          console.log('这是Anthropic API返回的错误，说明无效角色没有被过滤');
        }
      } catch (e) {
        // 忽略解析错误
      }
    }
    
  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

testInvalidRole().catch(console.error);
