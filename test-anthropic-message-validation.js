#!/usr/bin/env node

/**
 * Anthropic API 消息验证和发送测试
 * 测试改进后的消息清理和验证逻辑，确保避免空内容消息错误
 */

import fetch from 'node-fetch';

// 从环境变量获取API密钥
const API_KEY = process.env.ANTHROPIC_API_KEY || '************************************************************************************************************';

if (!API_KEY) {
  console.error('请设置 ANTHROPIC_API_KEY 环境变量');
  process.exit(1);
}

/**
 * 测试用例：各种消息格式的验证
 */
const testCases = [
  {
    name: '正常对话',
    messages: [
      { role: 'user', content: 'Hello, how are you?' },
      { role: 'assistant', content: 'I am doing well, thank you!' },
      { role: 'user', content: 'What can you help me with?' }
    ],
    shouldSucceed: true
  },
  {
    name: '包含空内容消息',
    messages: [
      { role: 'user', content: 'Hello' },
      { role: 'assistant', content: '' }, // 空内容
      { role: 'user', content: 'Are you there?' }
    ],
    shouldSucceed: true // 应该被清理后成功
  },
  {
    name: '包含只有空格的消息',
    messages: [
      { role: 'user', content: 'Hello' },
      { role: 'assistant', content: '   ' }, // 只有空格
      { role: 'user', content: 'Please respond' }
    ],
    shouldSucceed: true // 应该被清理后成功
  },
  {
    name: '包含system消息',
    messages: [
      { role: 'system', content: 'You are a helpful assistant.' },
      { role: 'user', content: 'Hello' },
      { role: 'assistant', content: 'Hi there!' },
      { role: 'user', content: 'How can you help?' }
    ],
    shouldSucceed: true
  },
  {
    name: '连续相同角色消息',
    messages: [
      { role: 'user', content: 'First message' },
      { role: 'user', content: 'Second message' }, // 连续用户消息
      { role: 'assistant', content: 'Response to both' },
      { role: 'user', content: 'Final message' }
    ],
    shouldSucceed: true // 应该被合并后成功
  },
  {
    name: '以assistant消息结尾',
    messages: [
      { role: 'user', content: 'Hello' },
      { role: 'assistant', content: 'Hi there!' },
      { role: 'user', content: 'How are you?' },
      { role: 'assistant', content: 'I am fine!' } // 以assistant结尾
    ],
    shouldSucceed: true // 应该移除末尾assistant消息后成功
  },
  {
    name: '只有system消息',
    messages: [
      { role: 'system', content: 'You are a helpful assistant.' }
    ],
    shouldSucceed: false // 没有对话消息，应该失败
  },
  {
    name: '空消息列表',
    messages: [],
    shouldSucceed: false
  },
  {
    name: '包含无效角色',
    messages: [
      { role: 'user', content: 'Hello' },
      { role: 'invalid_role', content: 'This should be filtered out' },
      { role: 'user', content: 'Are you there?' }
    ],
    shouldSucceed: true // 无效消息被过滤后应该成功
  },
  {
    name: '包含null/undefined内容',
    messages: [
      { role: 'user', content: 'Hello' },
      { role: 'assistant', content: null },
      { role: 'user', content: 'Please respond' }
    ],
    shouldSucceed: true // null内容被过滤后应该成功
  }
];

/**
 * 发送测试请求
 */
async function testMessageValidation(testCase) {
  console.log(`\n=== 测试: ${testCase.name} ===`);
  console.log('输入消息:', JSON.stringify(testCase.messages, null, 2));

  try {
    const requestBody = {
      model: 'claude-3-haiku-20240307',
      max_tokens: 500,
      messages: testCase.messages,
      temperature: 0.7
    };

    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 成功处理');
      if (data.content && data.content[0] && data.content[0].text) {
        console.log('响应:', data.content[0].text.substring(0, 100) + '...');
      }

      if (!testCase.shouldSucceed) {
        console.log('⚠️  警告: 预期失败但实际成功');
      }
    } else {
      const errorText = await response.text();
      console.log('❌ 失败:', errorText);

      if (testCase.shouldSucceed) {
        console.log('⚠️  警告: 预期成功但实际失败');
      }
    }

  } catch (error) {
    console.log('❌ 错误:', error.message);

    if (testCase.shouldSucceed) {
      console.log('⚠️  警告: 预期成功但实际失败');
    }
  }
}

/**
 * 测试流式消息发送
 */
async function testStreamMessage() {
  console.log('\n=== 测试流式消息发送 ===');

  const messages = [
    { role: 'user', content: 'Please tell me a short story about a robot.' }
  ];

  try {
    const requestBody = {
      model: 'claude-3-haiku-20240307',
      max_tokens: 1000,
      messages: messages,
      temperature: 0.7,
      stream: true
    };

    console.log('发送流式请求...');

    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ 流式请求失败:', errorText);
      return;
    }

    console.log('✅ 流式响应开始...');

    // 处理流式响应
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullResponse = '';

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);

            if (data === '[DONE]') {
              console.log('\n✅ 流式响应完成');
              return;
            }

            try {
              const parsed = JSON.parse(data);

              if (parsed.type === 'content_block_delta' && parsed.delta?.text) {
                process.stdout.write(parsed.delta.text);
                fullResponse += parsed.delta.text;
              }
            } catch (parseError) {
              // 忽略解析错误
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

  } catch (error) {
    console.log('❌ 流式测试错误:', error.message);
  }
}

/**
 * 测试带工具的消息发送
 */
async function testMessageWithTools() {
  console.log('\n=== 测试带工具的消息发送 ===');

  const messages = [
    { role: 'user', content: 'What is the current time?' }
  ];

  const tools = [
    {
      name: 'get_current_time',
      description: 'Get the current time',
      input_schema: {
        type: 'object',
        properties: {},
        required: []
      }
    }
  ];

  try {
    const requestBody = {
      model: 'claude-3-haiku-20240307',
      max_tokens: 500,
      messages: messages,
      tools: tools,
      temperature: 0.7
    };

    console.log('发送带工具的请求...');

    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 带工具的请求成功');
      console.log('响应:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('❌ 带工具的请求失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 带工具的测试错误:', error.message);
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('开始 Anthropic API 消息验证测试...\n');

  // 测试各种消息格式
  for (const testCase of testCases) {
    await testMessageValidation(testCase);
    await new Promise(resolve => setTimeout(resolve, 1000)); // 避免请求过快
  }

  // 测试流式消息
  await testStreamMessage();
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 测试带工具的消息
  await testMessageWithTools();

  console.log('\n测试完成！');
}

// 运行测试
runTests().catch(console.error);

export {
  testMessageValidation,
  testStreamMessage,
  testMessageWithTools,
  runTests
};
