#!/usr/bin/env node

/**
 * 调试工具调用JSON解析问题
 */

// 模拟您截图中看到的工具调用JSON格式
const testContent = `我将创作一首诗并保存到文件中。

{"type":"tool_calls","tool_calls":[{"name":"write_file","arguments":{"path":"/Users/<USER>/Desktop/poem.md","content":"# 生命之韵"},"result":{"success":true,"message":"文件已成功创建"},"success":true}]}

好的，我已经将诗保存到了文件中。`;

console.log('=== 调试工具调用JSON解析 ===\n');

console.log('测试内容:');
console.log(testContent);
console.log('\n' + '='.repeat(50) + '\n');

// 测试不同的正则表达式
const regexPatterns = [
  {
    name: '当前使用的正则',
    pattern: /\{"type"\s*:\s*"tool_calls"[\s\S]*?\}/g
  },
  {
    name: '更简单的正则',
    pattern: /\{"type":"tool_calls","tool_calls":\[.*?\]\}/g
  },
  {
    name: '更宽松的正则',
    pattern: /\{[^}]*"type"[^}]*"tool_calls"[^}]*\}/g
  },
  {
    name: '最宽松的正则',
    pattern: /\{.*?"type".*?"tool_calls".*?\}/gs
  }
];

regexPatterns.forEach((test, index) => {
  console.log(`测试 ${index + 1}: ${test.name}`);
  console.log(`正则表达式: ${test.pattern}`);
  
  const matches = testContent.match(test.pattern);
  
  if (matches) {
    console.log(`✅ 找到 ${matches.length} 个匹配项:`);
    matches.forEach((match, matchIndex) => {
      console.log(`  匹配 ${matchIndex + 1}:`);
      console.log(`    ${match.substring(0, 100)}${match.length > 100 ? '...' : ''}`);
      
      // 尝试解析JSON
      try {
        const parsed = JSON.parse(match);
        console.log(`    ✅ JSON解析成功`);
        console.log(`    类型: ${parsed.type}`);
        console.log(`    工具调用数量: ${parsed.tool_calls?.length || 0}`);
      } catch (e) {
        console.log(`    ❌ JSON解析失败: ${e.message}`);
      }
    });
  } else {
    console.log('❌ 没有找到匹配项');
  }
  
  console.log('\n' + '-'.repeat(30) + '\n');
});

// 手动提取JSON
console.log('手动提取JSON测试:');
const startIndex = testContent.indexOf('{"type":"tool_calls"');
if (startIndex !== -1) {
  let braceCount = 0;
  let endIndex = startIndex;
  
  for (let i = startIndex; i < testContent.length; i++) {
    if (testContent[i] === '{') {
      braceCount++;
    } else if (testContent[i] === '}') {
      braceCount--;
      if (braceCount === 0) {
        endIndex = i;
        break;
      }
    }
  }
  
  const extractedJson = testContent.substring(startIndex, endIndex + 1);
  console.log('提取的JSON:');
  console.log(extractedJson);
  
  try {
    const parsed = JSON.parse(extractedJson);
    console.log('✅ 手动提取的JSON解析成功');
    console.log('解析结果:', JSON.stringify(parsed, null, 2));
  } catch (e) {
    console.log('❌ 手动提取的JSON解析失败:', e.message);
  }
} else {
  console.log('❌ 没有找到JSON起始位置');
}

console.log('\n' + '='.repeat(50) + '\n');

// 测试实际的工具调用JSON格式
const actualToolCallJson = {
  "type": "tool_calls",
  "tool_calls": [{
    "name": "write_file",
    "arguments": {
      "path": "/Users/<USER>/Desktop/poem.md",
      "content": "# 生命之韵"
    },
    "result": {
      "success": true,
      "message": "文件已成功创建"
    },
    "success": true
  }]
};

const jsonString = JSON.stringify(actualToolCallJson);
console.log('标准工具调用JSON字符串:');
console.log(jsonString);

console.log('\n测试标准JSON字符串的匹配:');
regexPatterns.forEach((test, index) => {
  const matches = jsonString.match(test.pattern);
  console.log(`${test.name}: ${matches ? '✅ 匹配' : '❌ 不匹配'}`);
});

console.log('\n调试完成！');
