#!/usr/bin/env node

/**
 * 测试工具调用卡片显示功能
 * 模拟发送包含工具调用的消息，验证前端是否正确显示为卡片
 */

import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

/**
 * 测试工具调用请求
 */
async function testToolCallRequest() {
  console.log('=== 测试工具调用请求 ===\n');
  
  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: '请帮我写一首关于春天的诗，并保存到文件 spring_poem.md 中'
      }
    ],
    tools: [
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    temperature: 0.7
  };
  
  console.log('发送工具调用请求...');
  
  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log('响应状态:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 请求成功');
      console.log('响应数据:', JSON.stringify(data, null, 2));
      
      // 分析响应中的工具调用
      if (data.content) {
        let hasToolUse = false;
        for (const content of data.content) {
          if (content.type === 'tool_use') {
            hasToolUse = true;
            console.log('\n🎯 发现工具调用:');
            console.log('工具名称:', content.name);
            console.log('工具ID:', content.id);
            console.log('工具参数:');
            console.log(JSON.stringify(content.input, null, 2));
            
            // 模拟工具调用结果
            const toolResult = {
              type: 'tool_calls',
              tool_calls: [{
                name: content.name,
                arguments: content.input,
                result: {
                  success: true,
                  message: `文件 ${content.input.path} 已成功创建`,
                  path: content.input.path
                },
                success: true
              }]
            };
            
            console.log('\n📋 模拟的工具调用结果JSON (用于前端卡片显示):');
            console.log(JSON.stringify(toolResult, null, 2));
          }
        }
        
        if (!hasToolUse) {
          console.log('⚠️  响应中没有工具调用');
        }
      }
    } else {
      const errorText = await response.text();
      console.log('❌ 请求失败:', errorText);
    }
    
  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 测试直接发送包含工具调用JSON的消息
 */
async function testDirectToolCallMessage() {
  console.log('\n=== 测试直接发送工具调用JSON ===\n');
  
  // 模拟一个包含工具调用JSON的消息
  const toolCallJson = {
    type: 'tool_calls',
    tool_calls: [{
      name: 'write_file',
      arguments: {
        path: '/Users/<USER>/Desktop/test_poem.md',
        content: '# 春天的诗\n\n春风轻拂柳絮飞，\n花开满园蝶舞归。\n绿草如茵铺大地，\n万物复苏展新辉。'
      },
      result: {
        success: true,
        message: '文件已成功创建',
        path: '/Users/<USER>/Desktop/test_poem.md'
      },
      success: true
    }]
  };
  
  console.log('工具调用JSON格式:');
  console.log(JSON.stringify(toolCallJson, null, 2));
  
  console.log('\n这个JSON应该在前端被解析并显示为工具调用卡片。');
  console.log('卡片应该显示:');
  console.log('- 工具名称: write_file');
  console.log('- 状态: 调用成功');
  console.log('- 输入参数: path 和 content');
  console.log('- 调用结果: success, message, path');
}

/**
 * 测试多个工具调用
 */
async function testMultipleToolCalls() {
  console.log('\n=== 测试多个工具调用 ===\n');
  
  const multipleToolCallsJson = {
    type: 'tool_calls',
    tool_calls: [
      {
        name: 'write_file',
        arguments: {
          path: '/Users/<USER>/Desktop/poem1.md',
          content: '# 春天的诗\n\n春风轻拂...'
        },
        result: {
          success: true,
          message: '文件 poem1.md 已创建'
        },
        success: true
      },
      {
        name: 'write_file',
        arguments: {
          path: '/Users/<USER>/Desktop/poem2.md',
          content: '# 夏天的诗\n\n夏日炎炎...'
        },
        result: {
          success: true,
          message: '文件 poem2.md 已创建'
        },
        success: true
      }
    ]
  };
  
  console.log('多个工具调用JSON格式:');
  console.log(JSON.stringify(multipleToolCallsJson, null, 2));
  
  console.log('\n这应该显示为两个独立的工具调用卡片。');
}

/**
 * 测试工具调用失败的情况
 */
async function testFailedToolCall() {
  console.log('\n=== 测试工具调用失败 ===\n');
  
  const failedToolCallJson = {
    type: 'tool_calls',
    tool_calls: [{
      name: 'write_file',
      arguments: {
        path: '/invalid/path/test.md',
        content: '测试内容'
      },
      error: '无法创建文件：路径不存在',
      success: false
    }]
  };
  
  console.log('失败的工具调用JSON格式:');
  console.log(JSON.stringify(failedToolCallJson, null, 2));
  
  console.log('\n这应该显示为失败状态的工具调用卡片，带有错误信息。');
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始工具调用卡片显示测试...\n');
  
  await testToolCallRequest();
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  await testDirectToolCallMessage();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testMultipleToolCalls();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testFailedToolCall();
  
  console.log('\n测试完成！');
  console.log('\n📝 注意事项:');
  console.log('1. 确保前端应用正在运行');
  console.log('2. 检查浏览器控制台的日志输出');
  console.log('3. 验证工具调用JSON是否被正确解析为卡片组件');
  console.log('4. 检查卡片的展开/折叠功能是否正常');
}

// 运行测试
runAllTests().catch(console.error);
